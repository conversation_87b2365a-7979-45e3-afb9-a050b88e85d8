package UI.DetectionClass
{
   import UI.CheatData.CheatData;
   import UI.Equipments.EquipmentVO.EquipmentVO;
   import UI.GamingUI;
   import YJFY.Part1;
   
   public class DetectionClass2
   {
      private static var _instance:DetectionClass2 = null;
      
      public function DetectionClass2()
      {
         super();
         if(!_instance)
         {
            _instance = this;
            return;
         }
         throw new Error("fuck you! 没看见实例已经存在了吗！？");
      }
      
      public static function getInstance() : DetectionClass2
      {
         if(!_instance)
         {
            _instance = new DetectionClass2();
         }
         return _instance;
      }
      
      public function detectionEquipmentVOsQuoteIsSame(param1:Vector.<EquipmentVO>) : void
      {
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         var _loc4_:int = 0;
         var _loc5_:int = 0;
         if(param1 == null)
         {
            return;
         }
         _loc2_ = int(param1.length);
         _loc3_ = 0;
         while(_loc3_ < _loc2_)
         {
            _loc4_ = _loc3_ + 1;
            while(_loc4_ < _loc2_)
            {
               if(param1[_loc3_] == param1[_loc4_] && param1[_loc3_] != null)
               {
                  GamingUI.getInstance().closeInternalPanel();
                  CheatData.getInstance().addCheatDataStr("装备引用出现错误！");
                  throw new Error("装备引用出现错误！");
               }
               _loc4_++;
            }
            _loc3_++;
         }
      }
   }
}

