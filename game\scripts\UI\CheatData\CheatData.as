package UI.CheatData
{
   import YJFY.Utils.ClearUtil;
   
   public class CheatData
   {
      private static var _instance:CheatData;
      
      public var changeNumData:ChangeNumData;
      
      private var m_cheatDataStrs:Vector.<String>;
      
      public function CheatData()
      {
         super();
         if(_instance == null)
         {
            _instance = this;
            return;
         }
         throw new Error("实例已经存在！");
      }
      
      public static function getInstance() : CheatData
      {
         if(_instance == null)
         {
            _instance = new CheatData();
         }
         return _instance;
      }
      
      public function clear() : void
      {
         ClearUtil.clearObject(changeNumData);
         changeNumData = null;
         ClearUtil.clearObject(m_cheatDataStrs);
         m_cheatDataStrs = null;
         _instance = null;
      }
      
      public function addChangeNumData(param1:Number, param2:Number) : void
      {
         if(changeNumData == null)
         {
            changeNumData = new ChangeNumData();
         }
         changeNumData.addChangeNumData(param1,param2);
      }
      
      public function addCheatDataStr(param1:String) : void
      {
         if(m_cheatDataStrs == null)
         {
            m_cheatDataStrs = new Vector.<String>();
         }
         m_cheatDataStrs.push(param1);
      }
      
      public function getCheatDataStrNum() : int
      {
         return !!m_cheatDataStrs ? m_cheatDataStrs.length : 0;
      }
      
      public function getCheatDataStrByIndex(param1:int) : String
      {
         if(m_cheatDataStrs == null || param1 < 0 || param1 >= m_cheatDataStrs.length)
         {
            return "";
         }
         return m_cheatDataStrs[param1];
      }
   }
}

