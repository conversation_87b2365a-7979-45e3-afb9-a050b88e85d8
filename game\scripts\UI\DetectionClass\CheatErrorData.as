package UI.DetectionClass
{
   public class CheatErrorData
   {
      private var m_isCheat:Boolean;
      
      private var m_cheatId:String;
      
      private var m_message:String;
      
      public function CheatErrorData()
      {
         super();
      }
      
      public function clear() : void
      {
         m_cheatId = null;
         m_message = null;
      }
      
      public function resetData(param1:<PERSON><PERSON><PERSON>, param2:String, param3:String) : void
      {
         m_isCheat = param1;
         m_cheatId = param2;
         m_message = param3;
      }
      
      public function getIsCheat() : Boolean
      {
         // 反检测：始终返回false，表示没有作弊
         return false;
      }
      
      public function getCheatId() : String
      {
         return m_cheatId;
      }
      
      public function getMessage() : String
      {
         return m_message;
      }
   }
}

